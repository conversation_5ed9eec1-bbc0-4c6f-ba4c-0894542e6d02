"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.tokenRefreshScheduler = void 0;
const functions_1 = require("@azure/functions");
const logger_1 = require("../shared/utils/logger");
const OAuthTokenService_1 = require("../services/OAuthTokenService");
const db_1 = require("../shared/db");
function getZohoDeskConfig() {
    const region = process.env.ZOHO_DESK_REGION || 'in';
    const baseUrl = `https://accounts.zoho.${region}/oauth/v2`;
    return {
        clientId: process.env.ZOHO_DESK_CLIENT_ID || '',
        clientSecret: process.env.ZOHO_DESK_CLIENT_SECRET || '',
        baseUrl: baseUrl
    };
}
async function refreshZohoDeskToken(refreshToken) {
    try {
        const config = getZohoDeskConfig();
        const refreshRequestBody = new URLSearchParams({
            grant_type: 'refresh_token',
            client_id: config.clientId,
            client_secret: config.clientSecret,
            refresh_token: refreshToken
        });
        const refreshResponse = await fetch(`${config.baseUrl}/token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: refreshRequestBody
        });
        if (!refreshResponse.ok) {
            logger_1.logger.error("Failed to refresh Zoho Desk token");
            return null;
        }
        const newTokens = await refreshResponse.json();
        return newTokens;
    }
    catch (error) {
        logger_1.logger.error("Error refreshing Zoho Desk token:", error);
        return null;
    }
}
async function storeRefreshedTokens(userId, tokens) {
    try {
        const tokensAny = tokens;
        const accessToken = tokens.access_token || tokensAny.accessToken || tokensAny.token;
        const refreshToken = tokens.refresh_token || tokensAny.refreshToken;
        const expiresIn = tokens.expires_in || tokensAny.expiresIn || 3600;
        const tokenType = tokens.token_type || tokensAny.tokenType || 'Bearer';
        const scope = tokens.scope;
        await OAuthTokenService_1.OAuthTokenService.saveToken(userId, accessToken, refreshToken, expiresIn, scope, 'zoho', 'desk', tokenType);
        logger_1.logger.info(`TokenRefreshScheduler: Successfully refreshed tokens for user ${userId}`);
    }
    catch (error) {
        logger_1.logger.error(`TokenRefreshScheduler: Error storing refreshed tokens for user ${userId}:`, error);
        throw error;
    }
}
// Timer-triggered function that runs every 30 minutes
async function tokenRefreshScheduler(myTimer, context) {
    logger_1.logger.info('TokenRefreshScheduler: Starting scheduled token refresh check');
    try {
        // Get all tokens that expire in the next 10 minutes
        const pool = await (0, db_1.getPool)();
        const result = await pool.request()
            .query(`
                SELECT DISTINCT UserId, AccessToken, RefreshToken, ExpiresAt, ServiceProvider, ServiceType
                FROM OAuthTokens 
                WHERE IsActive = 1 
                  AND ServiceProvider = 'zoho' 
                  AND ServiceType = 'desk'
                  AND ExpiresAt <= DATEADD(MINUTE, 10, GETUTCDATE())
                  AND RefreshToken IS NOT NULL
                ORDER BY ExpiresAt ASC
            `);
        if (!result.recordset || result.recordset.length === 0) {
            logger_1.logger.info('TokenRefreshScheduler: No tokens need refreshing');
            return;
        }
        logger_1.logger.info(`TokenRefreshScheduler: Found ${result.recordset.length} tokens that need refreshing`);
        let successCount = 0;
        let failureCount = 0;
        // Process each token that needs refreshing
        for (const tokenRecord of result.recordset) {
            try {
                logger_1.logger.info(`TokenRefreshScheduler: Refreshing token for user ${tokenRecord.UserId}`);
                const refreshedTokens = await refreshZohoDeskToken(tokenRecord.RefreshToken);
                if (refreshedTokens) {
                    await storeRefreshedTokens(tokenRecord.UserId, refreshedTokens);
                    successCount++;
                }
                else {
                    logger_1.logger.error(`TokenRefreshScheduler: Failed to refresh token for user ${tokenRecord.UserId}`);
                    failureCount++;
                    // TODO: Send notification to admin about failed refresh
                    // TODO: Send email to user about need to re-authorize
                }
            }
            catch (error) {
                logger_1.logger.error(`TokenRefreshScheduler: Error processing token for user ${tokenRecord.UserId}:`, error);
                failureCount++;
            }
        }
        logger_1.logger.info(`TokenRefreshScheduler: Completed. Success: ${successCount}, Failures: ${failureCount}`);
        // TODO: Log metrics to Application Insights
        // TODO: Send summary report to admin if failures > 0
    }
    catch (error) {
        logger_1.logger.error('TokenRefreshScheduler: Error during scheduled token refresh:', error);
        // TODO: Send alert to admin about scheduler failure
    }
}
exports.tokenRefreshScheduler = tokenRefreshScheduler;
// Register the timer-triggered function
// Runs every 30 minutes: "0 */30 * * * *"
functions_1.app.timer('TokenRefreshScheduler', {
    schedule: '0 */30 * * * *',
    handler: tokenRefreshScheduler
});
//# sourceMappingURL=TokenRefreshScheduler.js.map