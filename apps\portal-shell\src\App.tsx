// React import removed as it's not needed in modern React with JSX transform
import './App.css';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { useState, useEffect } from 'react';


// MSAL Imports
import { AuthenticatedTemplate, UnauthenticatedTemplate } from "@azure/msal-react";

// Import Layouts and Pages/Components
import ShellLayout from './components/layout/ShellLayout';
import DashboardSection from './components/DashboardSection';
import ProtectedRoute from './components/ProtectedRoute';
import LoginScreen from './components/LoginScreen';
// Hub/Page components
import ComingSoonPage from './pages/ComingSoonPage'; // Import ComingSoon
import ITPage from './pages/ITPage'; // Import IT Page
import DocumentsPage from './pages/DocumentsPage';
// Import new user management pages
import UserManagementPage from './pages/AdminHub/UserManagementPage';
import UserEditPage from './pages/AdminHub/UserEditPage';
import UserAddPage from './pages/AdminHub/UserAddPage';
// Import Role Management page
import RoleManagementPage from './pages/AdminHub/RoleManagementPage';
// Import Portal Admin landing page
import PortalAdminPage from './pages/AdminHub/PortalAdminPage';

// Knowledge Hub
import KnowledgeHubPage from './pages/KnowledgeHubPage';

// IT Hub pages
import ITTicketsPage from './pages/ITHub/ITTicketsPage';
import ITAssetsPage from './pages/ITHub/ITAssetsPage';
import ZohoDeskSetup from './pages/ITHub/ZohoDeskSetup';
import ChangeManagementPage from './pages/ITHub/ChangeManagementPage';
import ChangeRequestDetailsPage from './pages/ITHub/ChangeRequestDetailsPage';


import Enterprises_Section from './pages/Enterprises_section';
import Execution_Mgnt from './pages/execution_mgnt';
import Quality_Page from './pages/quality_page';
import Sales_Page from './pages/sales_page';
import Scm_Page from './pages/scm_page';
import Stores_Page from './pages/stores_page';
import Production_Page from './pages/production_page';
import Hr_and_Admin from './pages/hr_and_admin';
import Portal_Dashboard from './pages/portal_dashboard';
import FinancePage from './pages/financepage';
import DandDPage from './pages/dandd';
import KnowledgeMgntPage from './pages/knowledge_mgnt';
// Removed duplicate import - using ITPage from line 17 instead
// LoginPrompt function removed - replaced with LoginScreen component


// Remove: import Fg_Reference_photos from './components/production/frontend/vite-project/src/Fg_App';
//./components/hr_portal/emp_adv/frontend/vite-project/src/Hr_App';

import Create_Reference_Photots from './components/production/frontend/vite-project/src/component/fg_reference_photos/create_reference_photots';
import Edit_Reference_Photots from './components/production/frontend/vite-project/src/component/fg_reference_photos/edit_fg_reference';
import MISReportPage from './components/production/frontend/vite-project/src/component/fg_reference_photos//MISReportPage';
import Sidebar from './components/production/frontend/vite-project/src/component/fg_reference_photos/sidebar';
import HeaderPage from './components/production/frontend/vite-project/src/component/fg_reference_photos/headerpage';
import Footer from './components/production/frontend/vite-project/src/component/fg_reference_photos/footer';
//C:\WAMP_SERVER\react_www\falcon\FalconHub\apps\portal-shell\src\\layout





import Employee_Advance from './components/hr_portal/emp_adv/frontend/vite-project/src/Hr_App';//'./components/hr_portal/emp_adv/frontend/vite-project/src/Hr_App';
//'./components/hr_portal/emp_adv/frontend/vite-project/src/Hr_App';
import Raise_Request from './components/hr_portal/emp_adv/frontend/vite-project/src/components/raise_request';
import Side_Bar_hr from './components/hr_portal/emp_adv/frontend/vite-project/src/components/layout/side_bar';
import HeaderPage_hr from './components/hr_portal/emp_adv/frontend/vite-project/src/components/layout/header_page';
import Approve_Request from './components/hr_portal/emp_adv/frontend/vite-project/src/components/approve_request';
import Approve_Payment from './components/hr_portal/emp_adv/frontend/vite-project/src/components/approve_payment';
import Update_Payment from './components/hr_portal/emp_adv/frontend/vite-project/src/components/update_payment';
import Mis_Report from './components/hr_portal/emp_adv/frontend/vite-project/src/components/mis_report';
//import Home_Page from './components/hr_portal/emp_adv/frontend/vite-project/src/components/home_page';
import UserRightsForm from './components/hr_portal/emp_adv/frontend/vite-project/src/components/UserRightsForm';





import Visual_Alert from './components/production/frontend/vite-project/src/VA_App';
import Create_Va from "./components/production/frontend/vite-project/src/component/visual_alert/create_va"
import Edit_Va from "./components/production/frontend/vite-project/src/component/visual_alert/edit_va"
import Update_Va from "./components/production/frontend/vite-project/src/component/visual_alert/update_va"
import RevisionApproval from "./components/production/frontend/vite-project/src/component/visual_alert/rev_approval"
import Header_Page_Va from "./components/production/frontend/vite-project/src/component/visual_alert/va_header_page"
import Footer_Va from "./components/production/frontend/vite-project/src/component/visual_alert/footer_va"
import Sidebar_Va from "./components/production/frontend/vite-project/src/component/visual_alert/sidebar_va"




import Non_Calibrated_Tool from './components/production/frontend/vite-project/src/Nc_App';
import Create_Nct from './components/production/frontend/vite-project/src/component/non_calibrated_tools/layout1';
import View_list from './components/production/frontend/vite-project/src/component/non_calibrated_tools/view_list';
import Mis_report_Cal   from './components/production/frontend/vite-project/src/component/non_calibrated_tools/mis_report';
import Pn_St_Mc from  './components/production/frontend/vite-project/src/component/non_calibrated_tools/pn_st_mc';
import View_St_Coc from  './components/production/frontend/vite-project/src/component/non_calibrated_tools/view_st_coc';


import Tool_Tracker from './components/production/frontend/vite-project/src/tool_tracker';
import Tool_Issue_Creation from './components/production/frontend/vite-project/src/component/TOOL ISSUE LOG/tool_issue_creation';
import Mis from './components/production/frontend/vite-project/src/component/TOOL ISSUE LOG/mis';





import Training_Portal from './components/production/frontend/vite-project/src/training_app';
import Cit_Master from './components/production/frontend/vite-project/src/component/training/cit_master';
import Create_New_Std_Certificate from './components/production/frontend/vite-project/src/component/training/create_new_std_certificate';
import View_Ipc_Details from './components/production/frontend/vite-project/src/component/training/view_ipcs';


import Vcs_App from './components/production/frontend/vite-project/src/Vcs_App';
import Create_Vd from './components/production/frontend/vite-project/src/component/vcs/create_vd';
import Visit_Registration from './components/production/frontend/vite-project/src/component/vcs/visit_registration';
import Invite_Details from './components/production/frontend/vite-project/src/component/vcs/invitee_details';
import Visitor_List_Form from './components/production/frontend/vite-project/src/component/vcs/visitors_list_form';
import Restricted_Area_Approvals from './components/production/frontend/vite-project/src/component/vcs/restricted_area_approvals';
import Visitor_Details from './components/production/frontend/vite-project/src/component/vcs/visitor_details';


import Kaizen_App from './components/production/frontend/vite-project/src/Kaizen_App';
import Create_Kaizen from './components/production/frontend/vite-project/src/component/kaizen/create_kaizen'





function App() {
   const [isManuallyAuthenticated, setIsManuallyAuthenticated] = useState(false);
   useEffect(() => {
    const user = localStorage.getItem('user');
    const userId = localStorage.getItem('login_userid');
    if (user && userId) {
      setIsManuallyAuthenticated(true);
    }
  }, []);

  // Listen for manual login events
  useEffect(() => {
    const handleStorageChange = () => {
      const user = localStorage.getItem('user');
      const userId = localStorage.getItem('login_userid');
      setIsManuallyAuthenticated(user && userId ? true : false);
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);
  const ConditionalRedirect = () => {
    let currentUser = null;

    try {
      currentUser = JSON.parse(localStorage.getItem('user') || 'null');
    } catch (e) {
      console.error("Invalid JSON in localStorage:", e);
      currentUser = null;
    }

    const loginUserId = currentUser?.employee_id || '';

    // Navigate based on whether user is logged in
    if (loginUserId) {
      return <Navigate to="/Portal_Dashboard" replace />;
    } else {
      return <Navigate to="/dashboard" replace />;
    }
  };

   const AppRoutes = () => (<Routes>
          <Route path="/" element={<ShellLayout />}>
           <Route index element={<ConditionalRedirect />} />
            <Route path="dashboard" element={<DashboardSection />} />

            {/* Hubs using ComingSoon or Placeholder - Default access for all authenticated users */}
            <Route path="knowledge" element={<KnowledgeHubPage />} />
            <Route path="it" element={<ITPage />} />
            <Route path="it/tickets" element={<ITTicketsPage />} />
            <Route path="it/assets" element={<ITAssetsPage />} />
            <Route path="it/change-management" element={<ChangeManagementPage />} />
            <Route path="it/change-management/:requestId" element={<ChangeRequestDetailsPage />} />
            <Route path="it/setup" element={
              <ProtectedRoute requiredRoles={['Administrator']}>
                <ZohoDeskSetup />
              </ProtectedRoute>
            } />
            <Route path="it/policies" element={<DocumentsPage />} />
            <Route path="hr" element={<ComingSoonPage pageName="HR Hub" />} />
            <Route path="admin-hub" element={<ComingSoonPage pageName="Admin Hub (Travel, etc.)" />} />

            {/* Portal Administration Routes - Restricted to Administrator role */}
            <Route path="portal-admin/user-management" element={
              <ProtectedRoute requiredRole="Administrator">
                <UserManagementPage />
              </ProtectedRoute>
            } />
            <Route path="portal-admin/add-user" element={
              <ProtectedRoute requiredRole="Administrator">
                <UserAddPage />
              </ProtectedRoute>
            } />
            <Route path="portal-admin/manage-user/:userId" element={
              <ProtectedRoute requiredRole="Administrator">
                <UserEditPage />
              </ProtectedRoute>
            } />
            <Route path="portal-admin/role-management" element={
              <ProtectedRoute requiredRole="Administrator">
                <RoleManagementPage />
              </ProtectedRoute>
            } />
            <Route path="portal-admin" element={
              <ProtectedRoute requiredRole="Administrator">
                <PortalAdminPage />
              </ProtectedRoute>
            } />

            <Route path="communication" element={<ComingSoonPage pageName="Communication Hub" />} />

            {/* Specific pages replaced with ComingSoon - Default access for all authenticated users */}
            <Route path="knowledge/documents" element={<ComingSoonPage pageName="Documents" />} />
            <Route path="communication/announcements" element={<ComingSoonPage pageName="Announcements" />} />
            <Route path="communication/events" element={<ComingSoonPage pageName="Events" />} />
            <Route path="actions" element={<ComingSoonPage pageName="Pending Actions" />} />

            {/* TODO: Add other routes like profile, settings, specific item views */}
            <Route path="*" element={<ComingSoonPage pageName="Page Not Found" />} />
            <Route path="*" element={<ComingSoonPage pageName="Page Not Found" />} />
            <Route path="/Enterprises_Section" element={<Enterprises_Section />} />
            <Route path="/execution_mgnt" element={<Execution_Mgnt />} />
            <Route path="/quality" element={<Quality_Page />} />
            <Route path="/production" element={<Production_Page />} />
            <Route path="/sales" element={<Sales_Page />} />
            <Route path="/scm" element={<Scm_Page />} />
            <Route path="/stores" element={<Stores_Page />} />
            <Route path="/hr_and_admin" element={<Hr_and_Admin />} />
            <Route path="/portal_dashboard" element={<Portal_Dashboard />} />
            <Route path="/finance_page" element={<FinancePage />} />
            <Route path="/dandd" element={<DandDPage />} />
            <Route path="/knowledge_mgnt" element={<KnowledgeMgntPage />} />
            <Route path="/it_page" element={<ITPage />} />

          <Route path="/Create_Reference_Photots" element={<Create_Reference_Photots />} />
          <Route path="/Edit_Reference_Photots" element={<Edit_Reference_Photots />} />
          <Route path="/MISReportPage" element={<MISReportPage />} />
          <Route path="/sidebar" element={<Sidebar isOpen={true} isMobile={false} />} />
          <Route path="/headerpage" element={<HeaderPage onToggleSidebar={() => {}} userData={null} onLogout={() => {}} />} />
          <Route path="/footer" element={<Footer />} />

            <Route path="employee_advance" element={<Employee_Advance />} />
            {/* Add other routes as needed */}
            <Route path="/Raise_Request" element={<Raise_Request />} />
            <Route path="/Side_Bar_hr" element={<Side_Bar_hr />} />
            <Route path="/HeaderPage_hr" element={<HeaderPage_hr />} />
            <Route path="/Approve_Request" element={<Approve_Request />} />
            <Route path="/Approve_Payment" element={<Approve_Payment />} />
            <Route path="/Update_Payment" element={<Update_Payment />} />
            <Route path="/Mis_Report" element={<Mis_Report />} />
            <Route path="/UserRightsForm" element={<UserRightsForm />} />


            <Route path="visual_alert" element={<Visual_Alert />} />
            <Route path="/Create_Va" element={<Create_Va />} />
            <Route path="/Edit_Va" element={<Edit_Va />} />
            <Route path="/Update_Va" element={<Update_Va />} />
            <Route path="/RevisionApproval" element={<RevisionApproval />} />
            <Route path="/Header_Page_Va" element={<Header_Page_Va onToggleSidebar={() => {}} userData={null} onLogout={() => {}} />} />
            <Route path="/Footer_Va" element={<Footer_Va />} />
            <Route path="/Sidebar_Va" element={<Sidebar_Va isOpen={true} isMobile={false} />} />

            <Route path="/Non_Calibrated_Tool" element={<Non_Calibrated_Tool/>} />
            <Route path="/Create_Nct" element={<Create_Nct/>}/>
            <Route path="/View_list" element={<View_list/>}/>
            <Route path="/Mis_report_Cal" element={<Mis_report_Cal/>}/>
            <Route path="/Pn_St_Mc" element={<Pn_St_Mc/>}/>
            <Route path="/View_St_Coc" element={<View_St_Coc/>}/>



            <Route path="/Tool_Tracker" element={<Tool_Tracker/>}/>
            <Route path="/Tool_Issue_Creation" element={<Tool_Issue_Creation/>}/>
            <Route path="/Mis" element={<Mis/>}/>


            <Route path="/Training_Portal" element={<Training_Portal/>}/>
            <Route path="/Cit_Master" element={<Cit_Master/>}/>
            <Route path="/Create_New_Std_Certificate" element={<Create_New_Std_Certificate/>}/>
            <Route path="/View_Ipc_Details" element={<View_Ipc_Details/>}/>


            <Route path="/Vcs_App" element={<Vcs_App/>}/>
            <Route path="/Create_Vd" element={<Create_Vd/>}/>
            <Route path="/Visit_Registration" element={<Visit_Registration/>}/>
            <Route path="/Invite_Details" element={<Invite_Details/>}/>
            <Route path="/Visitor_List_Form" element={<Visitor_List_Form/>}/>
            <Route path="/Restricted_Area_Approvals" element={<Restricted_Area_Approvals/>}/>
            <Route path="/Visitor_Details" element={<Visitor_Details/>}/>

            <Route path="/Kaizen_App" element={<Kaizen_App/>}/>
            <Route path="/Create_Kaizen" element={<Create_Kaizen/>}/>

          </Route>
        </Routes>
      );

  return (
    <BrowserRouter>
      {/* Check for manual authentication first */}
      {isManuallyAuthenticated ? (
        <AppRoutes />
      ) : (
        <>
          <AuthenticatedTemplate>
            <AppRoutes />
          </AuthenticatedTemplate>

          <UnauthenticatedTemplate>
            <LoginScreen />
          </UnauthenticatedTemplate>
        </>
      )}
    </BrowserRouter>
  );
}

export default App;