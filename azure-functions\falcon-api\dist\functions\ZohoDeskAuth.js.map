{"version": 3, "file": "ZohoDeskAuth.js", "sourceRoot": "", "sources": ["../../src/functions/ZohoDeskAuth.ts"], "names": [], "mappings": ";;AAAA,gDAAyF;AACzF,mDAAyD;AACzD,mDAAgD;AAChD,qEAAkE;AAmBlE,yDAAyD;AACzD,SAAS,iBAAiB;IACtB,0DAA0D;IAC1D,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,IAAI,CAAC,CAAC,6BAA6B;IAClF,MAAM,OAAO,GAAG,yBAAyB,MAAM,WAAW,CAAC;IAE3D,OAAO;QACH,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,EAAE;QAC/C,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,EAAE;QACvD,kEAAkE;QAClE,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,mDAAmD;QACtG,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE;YACJ,kBAAkB;YAClB,mBAAmB;YACnB,iBAAiB;SACpB;KACJ,CAAC;AACN,CAAC;AAED,2CAA2C;AAC3C,KAAK,UAAU,iBAAiB,CAAC,GAAgB,EAAE,OAA0B;IACzE,eAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;IAE/D,IAAI;QACA,uBAAuB;QACvB,MAAM,SAAS,GAAG,IAAA,8BAAkB,EAAC,GAAG,CAAC,CAAC;QAC1C,MAAM,aAAa,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;QAErD,IAAI,CAAC,aAAa,IAAI,CAAC,SAAS,EAAE;YAC9B,eAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YACzD,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,CAAC;SAC/D;QAED,MAAM,MAAM,GAAG,iBAAiB,EAAE,CAAC;QAEnC,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YAC1C,eAAM,CAAC,KAAK,CAAC,qDAAqD,CAAC,CAAC;YACpE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,CAAC;SAChF;QAED,mCAAmC;QACnC,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,GAAG,MAAM,CAAC,OAAO,OAAO,CAAC,CAAC;QAClD,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;QACrD,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC1D,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;QAChE,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QAC9D,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QACtD,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QAEjD,6CAA6C;QAC7C,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;YACrC,MAAM,EAAE,SAAS,EAAE,MAAM,IAAI,UAAU;YACvC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACvB,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAE5C,eAAM,CAAC,IAAI,CAAC,sDAAsD,SAAS,EAAE,MAAM,IAAI,UAAU,EAAE,CAAC,CAAC;QAErG,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE;gBAC3B,OAAO,EAAE,0DAA0D;aACtE;SACJ,CAAC;KAEL;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,mDAAmD,EAAE,KAAK,CAAC,CAAC;QACzE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,sCAAsC,EAAE,EAAE,CAAC;KACvF;AACL,CAAC;AAED,iEAAiE;AACjE,KAAK,UAAU,gBAAgB,CAAC,GAAgB,EAAE,OAA0B;IACxE,eAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;IAE3D,IAAI;QACA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC7B,MAAM,IAAI,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC1C,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAE5C,IAAI,KAAK,EAAE;YACP,eAAM,CAAC,KAAK,CAAC,kCAAkC,KAAK,EAAE,CAAC,CAAC;YACxD,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,+BAA+B,KAAK,EAAE,EAAE,EAAE,CAAC;SACvF;QAED,IAAI,CAAC,IAAI,EAAE;YACP,eAAM,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;YACjE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,8BAA8B,EAAE,EAAE,CAAC;SAC/E;QAED,MAAM,MAAM,GAAG,iBAAiB,EAAE,CAAC;QAEnC,+CAA+C;QAC/C,MAAM,gBAAgB,GAAG,IAAI,eAAe,CAAC;YACzC,UAAU,EAAE,oBAAoB;YAChC,SAAS,EAAE,MAAM,CAAC,QAAQ;YAC1B,aAAa,EAAE,MAAM,CAAC,YAAY;YAClC,YAAY,EAAE,MAAM,CAAC,WAAW;YAChC,IAAI,EAAE,IAAI;SACb,CAAC,CAAC;QAEH,0CAA0C;QAC1C,eAAM,CAAC,IAAI,CAAC,iDAAiD,MAAM,CAAC,OAAO,QAAQ,CAAC,CAAC;QACrF,eAAM,CAAC,IAAI,CAAC,kDAAkD,gBAAgB,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QAC7F,eAAM,CAAC,IAAI,CAAC,yCAAyC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;QAC3E,eAAM,CAAC,IAAI,CAAC,sCAAsC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QAErE,MAAM,aAAa,GAAG,MAAM,KAAK,CAAC,GAAG,MAAM,CAAC,OAAO,QAAQ,EAAE;YACzD,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACL,cAAc,EAAE,mCAAmC;aACtD;YACD,IAAI,EAAE,gBAAgB;SACzB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE;YACnB,MAAM,SAAS,GAAG,MAAM,aAAa,CAAC,IAAI,EAAE,CAAC;YAC7C,eAAM,CAAC,KAAK,CAAC,uDAAuD,aAAa,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC,CAAC;YAC1G,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,KAAK,EAAE,kDAAkD;oBACzD,UAAU,EAAE,aAAa,CAAC,MAAM;oBAChC,QAAQ,EAAE,SAAS;oBACnB,GAAG,EAAE,GAAG,MAAM,CAAC,OAAO,QAAQ;oBAC9B,WAAW,EAAE,gBAAgB,CAAC,QAAQ,EAAE;iBAC3C;aACJ,CAAC;SACL;QAED,MAAM,MAAM,GAAQ,MAAM,aAAa,CAAC,IAAI,EAAE,CAAC;QAE/C,yCAAyC;QACzC,eAAM,CAAC,IAAI,CAAC,iDAAiD,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAChG,eAAM,CAAC,IAAI,CAAC,0CAA0C,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAExF,kCAAkC;QAClC,IAAI,MAAM,CAAC,KAAK,EAAE;YACd,eAAM,CAAC,KAAK,CAAC,uCAAuC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;YACpE,eAAM,CAAC,KAAK,CAAC,wCAAwC,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC;YACjF,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,KAAK,EAAE,qBAAqB,MAAM,CAAC,KAAK,EAAE;oBAC1C,iBAAiB,EAAE,MAAM,CAAC,iBAAiB;oBAC3C,KAAK,EAAE,MAAM;iBAChB;aACJ,CAAC;SACL;QAED,4CAA4C;QAC5C,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YACtB,eAAM,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;YAC9D,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,KAAK,EAAE,oCAAoC;oBAC3C,KAAK,EAAE,MAAM;iBAChB;aACJ,CAAC;SACL;QAED,eAAM,CAAC,IAAI,CAAC,yCAAyC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,gBAAgB,EAAE,CAAC,CAAC;QAE1G,sCAAsC;QACtC,IAAI,MAAM,GAAG,SAAS,CAAC;QACvB,IAAI,KAAK,EAAE;YACP,IAAI;gBACA,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACtE,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;aAC7B;YAAC,OAAO,GAAG,EAAE;gBACV,eAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;aACpE;SACJ;QAED,uCAAuC;QACvC,MAAM,WAAW,GAAG,MAAwB,CAAC;QAE7C,wBAAwB;QACxB,gEAAgE;QAChE,sDAAsD;QACtD,MAAM,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAE/C,eAAM,CAAC,IAAI,CAAC,2DAA2D,MAAM,EAAE,CAAC,CAAC;QAEjF,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,gDAAgD;gBACzD,SAAS,EAAE,WAAW,CAAC,UAAU,IAAI,IAAI;gBACzC,KAAK,EAAE,WAAW,CAAC,KAAK;gBACxB,WAAW,EAAE,UAAU;gBACvB,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC9B,MAAM,EAAE,MAAM;aACjB;SACJ,CAAC;KAEL;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;QACpE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE,EAAE,CAAC;KACnF;AACL,CAAC;AAED,gEAAgE;AAChE,KAAK,UAAU,gBAAgB,CAAC,GAAgB,EAAE,OAA0B;IACxE,eAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;IAEtD,IAAI;QACA,uBAAuB;QACvB,MAAM,SAAS,GAAG,IAAA,8BAAkB,EAAC,GAAG,CAAC,CAAC;QAC1C,MAAM,aAAa,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;QAErD,IAAI,CAAC,aAAa,IAAI,CAAC,SAAS,EAAE;YAC9B,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,CAAC;SAC/D;QAED,MAAM,MAAM,GAAG,SAAS,EAAE,MAAM,IAAI,UAAU,CAAC;QAE/C,oBAAoB;QACpB,MAAM,MAAM,GAAG,MAAM,uBAAuB,CAAC,MAAM,CAAC,CAAC;QAErD,IAAI,CAAC,MAAM,EAAE;YACT,eAAM,CAAC,IAAI,CAAC,8CAA8C,MAAM,EAAE,CAAC,CAAC;YACpE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,2DAA2D,EAAE,EAAE,CAAC;SAC5G;QAED,kEAAkE;QAClE,yEAAyE;QACzE,MAAM,KAAK,GAAG,MAAM,qCAAiB,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7E,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,2DAA2D,EAAE,EAAE,CAAC;SAC5G;QAED,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,kBAAkB,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;QAErE,IAAI,KAAK,CAAC,SAAS,IAAI,kBAAkB,IAAI,KAAK,CAAC,YAAY,EAAE;YAC7D,gBAAgB;YAChB,MAAM,eAAe,GAAG,MAAM,oBAAoB,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YACvE,IAAI,eAAe,EAAE;gBACjB,MAAM,mBAAmB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;gBACnD,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,WAAW,EAAE,eAAe,CAAC,YAAY,EAAE,EAAE,CAAC;aACnF;iBAAM;gBACH,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,8CAA8C,EAAE,EAAE,CAAC;aAC/F;SACJ;QAED,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,WAAW,EAAE,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;KAExE;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;QAC9D,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,CAAC;KAC7E;AACL,CAAC;AAED,0CAA0C;AAC1C,KAAK,UAAU,oBAAoB,CAAC,YAAoB;IACpD,IAAI;QACA,MAAM,MAAM,GAAG,iBAAiB,EAAE,CAAC;QAEnC,MAAM,kBAAkB,GAAG,IAAI,eAAe,CAAC;YAC3C,UAAU,EAAE,eAAe;YAC3B,SAAS,EAAE,MAAM,CAAC,QAAQ;YAC1B,aAAa,EAAE,MAAM,CAAC,YAAY;YAClC,aAAa,EAAE,YAAY;SAC9B,CAAC,CAAC;QAEH,MAAM,eAAe,GAAG,MAAM,KAAK,CAAC,GAAG,MAAM,CAAC,OAAO,QAAQ,EAAE;YAC3D,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACL,cAAc,EAAE,mCAAmC;aACtD;YACD,IAAI,EAAE,kBAAkB;SAC3B,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE;YACrB,eAAM,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;YAClD,OAAO,IAAI,CAAC;SACf;QAED,MAAM,SAAS,GAAmB,MAAM,eAAe,CAAC,IAAI,EAAE,CAAC;QAC/D,OAAO,SAAS,CAAC;KAEpB;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC;KACf;AACL,CAAC;AAED,gFAAgF;AAChF,KAAK,UAAU,mBAAmB,CAAC,MAAc,EAAE,MAAsB;IACrE,IAAI;QACJ,oFAAoF;QACpF,MAAM,SAAS,GAAG,MAAa,CAAC;QAChC,MAAM,WAAW,GAAG,MAAM,CAAC,YAAY,IAAI,SAAS,CAAC,WAAW,IAAI,SAAS,CAAC,KAAK,CAAC;QACpF,MAAM,YAAY,GAAG,MAAM,CAAC,aAAa,IAAI,SAAS,CAAC,YAAY,CAAC;QACpE,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,IAAI,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC;QACnE,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,IAAI,SAAS,CAAC,SAAS,IAAI,QAAQ,CAAC;QACnE,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAE/B,eAAM,CAAC,IAAI,CAAC,qCAAqC,MAAM,gBAAgB,SAAS,WAAW,CAAC,CAAC;QAC7F,eAAM,CAAC,IAAI,CAAC,iDAAiD,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,oBAAoB,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;QAE9I,kDAAkD;QAClD,MAAM,qCAAiB,CAAC,SAAS,CAC7B,MAAM,EACN,WAAW,EACX,YAAY,EACZ,SAAS,EACT,KAAK,EACL,MAAM,EACN,MAAM,EACN,SAAS,CACZ,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,uDAAuD,MAAM,cAAc,CAAC,CAAC;KAC5F;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,iDAAiD,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;QAChF,MAAM,KAAK,CAAC;KACf;AACL,CAAC;AAED,KAAK,UAAU,uBAAuB,CAAC,MAAc;IACjD,IAAI;QACA,MAAM,KAAK,GAAG,MAAM,qCAAiB,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAE7E,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,IAAI,CAAC;SACf;QAED,kDAAkD;QAClD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;QAE9E,OAAO;YACH,YAAY,EAAE,KAAK,CAAC,WAAW;YAC/B,aAAa,EAAE,KAAK,CAAC,YAAY;YACjC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,gBAAgB,CAAC;YACzC,UAAU,EAAE,KAAK,CAAC,SAAS,IAAI,QAAQ;YACvC,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,SAAS,EAAE,GAAG,CAAC,gDAAgD;SAClE,CAAC;KACL;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,oDAAoD,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;QACnF,OAAO,IAAI,CAAC;KACf;AACL,CAAC;AAED,2BAA2B;AAC3B,eAAG,CAAC,IAAI,CAAC,mBAAmB,EAAE;IAC1B,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,SAAS,EAAE,WAAW;IACtB,KAAK,EAAE,0BAA0B;IACjC,OAAO,EAAE,iBAAiB;CAC7B,CAAC,CAAC;AAEH,eAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE;IACzB,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,SAAS,EAAE,WAAW;IACtB,KAAK,EAAE,yBAAyB;IAChC,OAAO,EAAE,gBAAgB;CAC5B,CAAC,CAAC;AAEH,eAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE;IACzB,OAAO,EAAE,CAAC,MAAM,CAAC;IACjB,SAAS,EAAE,WAAW;IACtB,KAAK,EAAE,sBAAsB;IAC7B,OAAO,EAAE,gBAAgB;CAC5B,CAAC,CAAC"}