import { app, HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
import { getPool } from '../shared/db';
import * as sql from 'mssql';

export async function getPolicies(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  try {
    const page = parseInt(req.query.get('page') || '1');
    const pageSize = parseInt(req.query.get('pageSize') || '10');
    const search = req.query.get('search') || '';
    const type = req.query.get('type') || '';

    const pool = await getPool();
    let where = 'WHERE IsActive = 1';
    const params: { name: string; type: any; value: any }[] = [];

    if (search) {
      where += ' AND (Title LIKE @search OR Description LIKE @search OR Tags LIKE @search)';
      params.push({ name: 'search', type: sql.NVar<PERSON>har, value: `%${search}%` });
    }
    if (type) {
      where += ' AND Type = @type';
      params.push({ name: 'type', type: sql.NVarChar, value: type });
    }

    const offset = (page - 1) * pageSize;
    const query = `
      SELECT * FROM Policies
      ${where}
      ORDER BY LastUpdated DESC
      OFFSET @offset ROWS FETCH NEXT @pageSize ROWS ONLY;
      SELECT COUNT(*) as total FROM Policies ${where};
    `;

    const request = pool.request();
    params.forEach(p => request.input(p.name, p.type, p.value));
    request.input('offset', sql.Int, offset);
    request.input('pageSize', sql.Int, pageSize);

    const result = await request.query(query);
    const policies = (result.recordsets && Array.isArray(result.recordsets) && result.recordsets[0]) ? result.recordsets[0] : [];
    const total = (result.recordsets && Array.isArray(result.recordsets) && result.recordsets[1] && result.recordsets[1][0]) ? result.recordsets[1][0].total : 0;

    return {
      status: 200,
      jsonBody: {
        policies,
        total,
        page,
        pageSize
      }
    };
  } catch (err) {
    context.error('GetPolicies error:', err);
    return {
      status: 500,
      jsonBody: { error: 'Failed to fetch policies.' }
    };
  }
}

app.http('GetPolicies', {
  methods: ['GET'],
  authLevel: 'function',
  handler: getPolicies
});