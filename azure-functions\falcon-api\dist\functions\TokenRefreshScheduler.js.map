{"version": 3, "file": "TokenRefreshScheduler.js", "sourceRoot": "", "sources": ["../../src/functions/TokenRefreshScheduler.ts"], "names": [], "mappings": ";;;AAAA,gDAAiE;AACjE,mDAAgD;AAChD,qEAAkE;AAClE,qCAAuC;AAkBvC,SAAS,iBAAiB;IACtB,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,IAAI,CAAC;IACpD,MAAM,OAAO,GAAG,yBAAyB,MAAM,WAAW,CAAC;IAE3D,OAAO;QACH,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,EAAE;QAC/C,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,EAAE;QACvD,OAAO,EAAE,OAAO;KACnB,CAAC;AACN,CAAC;AAED,KAAK,UAAU,oBAAoB,CAAC,YAAoB;IACpD,IAAI;QACA,MAAM,MAAM,GAAG,iBAAiB,EAAE,CAAC;QAEnC,MAAM,kBAAkB,GAAG,IAAI,eAAe,CAAC;YAC3C,UAAU,EAAE,eAAe;YAC3B,SAAS,EAAE,MAAM,CAAC,QAAQ;YAC1B,aAAa,EAAE,MAAM,CAAC,YAAY;YAClC,aAAa,EAAE,YAAY;SAC9B,CAAC,CAAC;QAEH,MAAM,eAAe,GAAG,MAAM,KAAK,CAAC,GAAG,MAAM,CAAC,OAAO,QAAQ,EAAE;YAC3D,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACL,cAAc,EAAE,mCAAmC;aACtD;YACD,IAAI,EAAE,kBAAkB;SAC3B,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE;YACrB,eAAM,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;YAClD,OAAO,IAAI,CAAC;SACf;QAED,MAAM,SAAS,GAAmB,MAAM,eAAe,CAAC,IAAI,EAAE,CAAC;QAC/D,OAAO,SAAS,CAAC;KAEpB;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC;KACf;AACL,CAAC;AAED,KAAK,UAAU,oBAAoB,CAAC,MAAc,EAAE,MAAsB;IACtE,IAAI;QACA,MAAM,SAAS,GAAG,MAAa,CAAC;QAChC,MAAM,WAAW,GAAG,MAAM,CAAC,YAAY,IAAI,SAAS,CAAC,WAAW,IAAI,SAAS,CAAC,KAAK,CAAC;QACpF,MAAM,YAAY,GAAG,MAAM,CAAC,aAAa,IAAI,SAAS,CAAC,YAAY,CAAC;QACpE,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,IAAI,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC;QACnE,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,IAAI,SAAS,CAAC,SAAS,IAAI,QAAQ,CAAC;QACvE,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAE3B,MAAM,qCAAiB,CAAC,SAAS,CAC7B,MAAM,EACN,WAAW,EACX,YAAY,EACZ,SAAS,EACT,KAAK,EACL,MAAM,EACN,MAAM,EACN,SAAS,CACZ,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,iEAAiE,MAAM,EAAE,CAAC,CAAC;KAC1F;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,kEAAkE,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;QACjG,MAAM,KAAK,CAAC;KACf;AACL,CAAC;AAED,sDAAsD;AAC/C,KAAK,UAAU,qBAAqB,CAAC,OAAc,EAAE,OAA0B;IAClF,eAAM,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;IAE7E,IAAI;QACA,oDAAoD;QACpD,MAAM,IAAI,GAAG,MAAM,IAAA,YAAO,GAAE,CAAC;QAC7B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE;aAC9B,KAAK,CAAC;;;;;;;;;aASN,CAAC,CAAC;QAEP,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACpD,eAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;YAChE,OAAO;SACV;QAED,eAAM,CAAC,IAAI,CAAC,gCAAgC,MAAM,CAAC,SAAS,CAAC,MAAM,8BAA8B,CAAC,CAAC;QAEnG,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,2CAA2C;QAC3C,KAAK,MAAM,WAAW,IAAI,MAAM,CAAC,SAAS,EAAE;YACxC,IAAI;gBACA,eAAM,CAAC,IAAI,CAAC,oDAAoD,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;gBAEtF,MAAM,eAAe,GAAG,MAAM,oBAAoB,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;gBAE7E,IAAI,eAAe,EAAE;oBACjB,MAAM,oBAAoB,CAAC,WAAW,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;oBAChE,YAAY,EAAE,CAAC;iBAClB;qBAAM;oBACH,eAAM,CAAC,KAAK,CAAC,2DAA2D,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;oBAC9F,YAAY,EAAE,CAAC;oBAEf,wDAAwD;oBACxD,sDAAsD;iBACzD;aAEJ;YAAC,OAAO,KAAK,EAAE;gBACZ,eAAM,CAAC,KAAK,CAAC,0DAA0D,WAAW,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;gBACrG,YAAY,EAAE,CAAC;aAClB;SACJ;QAED,eAAM,CAAC,IAAI,CAAC,8CAA8C,YAAY,eAAe,YAAY,EAAE,CAAC,CAAC;QAErG,4CAA4C;QAC5C,qDAAqD;KAExD;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,8DAA8D,EAAE,KAAK,CAAC,CAAC;QACpF,oDAAoD;KACvD;AACL,CAAC;AA7DD,sDA6DC;AAED,wCAAwC;AACxC,0CAA0C;AAC1C,eAAG,CAAC,KAAK,CAAC,uBAAuB,EAAE;IAC/B,QAAQ,EAAE,gBAAgB;IAC1B,OAAO,EAAE,qBAAqB;CACjC,CAAC,CAAC"}