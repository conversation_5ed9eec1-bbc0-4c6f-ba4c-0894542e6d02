import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Plus, Search, X } from 'feather-icons-react';
import { useCurrentUser } from '../services/userContext';

const hasElevatedAccess = (roles: string[] = []) =>
  roles.includes('Administrator') || roles.includes('IT Admin');

interface Policy {
  PolicyID: number;
  Title: string;
  Type: string;
  Description: string;
  Tags: string;
  Version: string;
  FileUrl: string;
  LastUpdated: string;
  UploadedBy: string;
}

const typeOptions = [
  'IT Policy',
  'Guideline',
  'Procedure',
  'Form',
];

function UploadPolicyModal({ open, onClose, onSuccess }: { open: boolean; onClose: () => void; onSuccess: () => void }) {
  const [title, setTitle] = useState('');
  const [type, setType] = useState(typeOptions[0]);
  const [description, setDescription] = useState('');
  const [tags, setTags] = useState('');
  const [version, setVersion] = useState('v1.0');
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(false);
    if (!file) {
      setError('Please select a PDF file.');
      return;
    }
    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('title', title);
      formData.append('type', type);
      formData.append('description', description);
      formData.append('tags', tags);
      formData.append('version', version);
      formData.append('file', file);
      // Optionally add uploadedBy if available
      const res = await fetch('/api/policies/upload', {
        method: 'POST',
        body: formData,
        headers: {
          // x-roles header for RBAC (simulate, backend should use real auth in prod)
          'x-roles': localStorage.getItem('userRoles') || '',
        },
      });
      if (!res.ok) {
        const data = await res.json().catch(() => ({}));
        throw new Error(data.error || 'Upload failed');
      }
      setSuccess(true);
      setTitle(''); setType(typeOptions[0]); setDescription(''); setTags(''); setVersion('v1.0'); setFile(null);
      onSuccess();
      setTimeout(() => { setSuccess(false); onClose(); }, 1200);
    } catch (err: any) {
      setError(err.message || 'Upload failed');
    } finally {
      setLoading(false);
    }
  };

  if (!open) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
      <div className="bg-white rounded-xl shadow-lg p-8 w-full max-w-md relative">
        <button className="absolute top-3 right-3 text-gray-400 hover:text-gray-700" onClick={onClose}><X size={20} /></button>
        <h2 className="text-xl font-bold mb-4">Upload New Policy</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Title</label>
            <input type="text" className="w-full border rounded px-3 py-2" value={title} onChange={e => setTitle(e.target.value)} required />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Type</label>
            <select className="w-full border rounded px-3 py-2" value={type} onChange={e => setType(e.target.value)} required>
              {typeOptions.map(opt => <option key={opt} value={opt}>{opt}</option>)}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Description</label>
            <textarea className="w-full border rounded px-3 py-2" value={description} onChange={e => setDescription(e.target.value)} rows={2} />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Tags</label>
            <input type="text" className="w-full border rounded px-3 py-2" value={tags} onChange={e => setTags(e.target.value)} placeholder="Comma-separated" />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Version</label>
            <input type="text" className="w-full border rounded px-3 py-2" value={version} onChange={e => setVersion(e.target.value)} />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">PDF File</label>
            <input type="file" accept="application/pdf" onChange={e => setFile(e.target.files?.[0] || null)} required />
          </div>
          {error && <div className="text-red-500 text-sm">{error}</div>}
          {success && <div className="text-green-600 text-sm">Upload successful!</div>}
          <button type="submit" className="w-full bg-blue-600 text-white py-2 rounded font-semibold mt-2" disabled={loading}>{loading ? 'Uploading...' : 'Upload'}</button>
        </form>
      </div>
    </div>
  );
}

const DocumentsPage: React.FC = () => {
  const { user } = useCurrentUser();
  const [policies, setPolicies] = useState<Policy[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(1);
  const [pageSize] = useState(9);
  const [total, setTotal] = useState(0);
  const [showUpload, setShowUpload] = useState(false);

  useEffect(() => {
    const fetchPolicies = async () => {
      setLoading(true);
      setError(null);
      try {
        const params = new URLSearchParams({
          page: page.toString(),
          pageSize: pageSize.toString(),
        });
        if (search) params.append('search', search);
        const res = await fetch(`/api/policies?${params.toString()}`);
        if (!res.ok) throw new Error('Failed to fetch policies');
        const data = await res.json();
        setPolicies(data.policies || []);
        setTotal(data.total || 0);
      } catch (err: any) {
        setError(err.message || 'Error fetching policies');
      } finally {
        setLoading(false);
      }
    };
    fetchPolicies();
  }, [search, page, pageSize]);

  return (
    <div className="p-8 min-h-screen bg-white">
      {/* Breadcrumbs */}
      <nav className="text-sm text-gray-500 mb-6">
        <Link to="/it" className="hover:underline">IT Hub</Link> <span className="mx-2">/</span> <span className="text-gray-700 font-semibold">Policies</span>
      </nav>

      {/* Header and Upload */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4">
        <h1 className="text-2xl font-bold text-gray-900">IT Policies & Documents</h1>
        {hasElevatedAccess(user?.roles) && (
          <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg shadow hover:bg-blue-700 transition-colors" onClick={() => setShowUpload(true)}>
            <Plus size={18} className="mr-2" /> Upload Document
          </button>
        )}
      </div>

      <UploadPolicyModal open={showUpload} onClose={() => setShowUpload(false)} onSuccess={() => { setPage(1); }} />

      {/* Search Bar */}
      <div className="mb-8 max-w-xl">
        <div className="relative">
          <span className="absolute left-3 top-2.5 text-gray-400">
            <Search size={18} />
          </span>
          <input
            type="text"
            className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-200 focus:ring-2 focus:ring-blue-200 focus:outline-none text-gray-900 bg-gray-50"
            placeholder="Search IT policies and documents..."
            value={search}
            onChange={e => { setSearch(e.target.value); setPage(1); }}
          />
        </div>
      </div>

      {/* Loading/Error States */}
      {loading && <div className="text-center py-8 text-gray-500">Loading policies...</div>}
      {error && <div className="text-center py-8 text-red-500">{error}</div>}

      {/* Document Card Grid */}
      {!loading && !error && (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {policies.map(doc => (
              <div key={doc.PolicyID} className="bg-white rounded-xl shadow p-6 flex flex-col">
                <div className="flex items-center mb-2">
                  <span className="px-2 py-1 text-xs rounded bg-blue-100 text-blue-700 font-medium mr-2">{doc.Type}</span>
                  <span className="text-xs text-gray-400 ml-auto">{doc.Version}</span>
                </div>
                <div className="font-semibold text-lg text-gray-900 truncate mb-1">{doc.Title}</div>
                <div className="text-sm text-gray-600 mb-3 truncate">{doc.Description}</div>
                <div className="text-xs text-gray-400 mb-4">Last updated: {doc.LastUpdated}</div>
                <div className="mt-auto flex gap-2">
                  <a href={doc.FileUrl} target="_blank" rel="noopener noreferrer" className="px-3 py-1 bg-blue-50 text-blue-700 rounded hover:bg-blue-100 text-xs font-medium">View</a>
                  <a href={doc.FileUrl} download className="px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 text-xs font-medium">Download</a>
                </div>
              </div>
            ))}
          </div>
          {/* Pagination */}
          <div className="flex justify-center mt-8 gap-2">
            <button
              className="px-3 py-1 rounded bg-gray-100 text-gray-700 hover:bg-gray-200 text-xs font-medium"
              onClick={() => setPage(p => Math.max(1, p - 1))}
              disabled={page === 1}
            >
              Previous
            </button>
            <span className="px-3 py-1 text-xs text-gray-600">Page {page} of {Math.ceil(total / pageSize) || 1}</span>
            <button
              className="px-3 py-1 rounded bg-gray-100 text-gray-700 hover:bg-gray-200 text-xs font-medium"
              onClick={() => setPage(p => p + 1)}
              disabled={page * pageSize >= total}
            >
              Next
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default DocumentsPage; 