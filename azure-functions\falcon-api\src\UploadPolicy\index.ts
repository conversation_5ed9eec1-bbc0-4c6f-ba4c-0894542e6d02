import { app, HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
import { getPool } from '../shared/db';
import * as sql from 'mssql';
import { BlobServiceClient } from '@azure/storage-blob';

function hasElevatedAccess(roles: string[] = []) {
  return roles.includes('Administrator') || roles.includes('IT Admin');
}

export async function uploadPolicy(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  try {
    const userRoles = (req.headers.get('x-roles') || '').split(',').map(r => r.trim());
    if (!hasElevatedAccess(userRoles)) {
      return { status: 403, jsonBody: { error: 'Forbidden: Insufficient permissions.' } };
    }

    // Parse form-data (assume PDF file and metadata fields)
    const formData = await req.formData();
    const file = formData.get('file');
    if (!(file instanceof Blob) || file.type !== 'application/pdf') {
      return { status: 400, jsonBody: { error: 'Only PDF files are allowed.' } };
    }
    const title = formData.get('title') as string;
    const type = formData.get('type') as string;
    const description = formData.get('description') as string;
    const tags = formData.get('tags') as string;
    const version = (formData.get('version') as string) || 'v1.0';
    const uploadedBy = formData.get('uploadedBy') as string || 'Unknown';

    // Upload PDF to Azure Blob Storage
    const blobService = BlobServiceClient.fromConnectionString(process.env.AZURE_STORAGE_CONNECTION_STRING!);
    const container = blobService.getContainerClient(process.env.POLICIES_BLOB_CONTAINER || 'policies');
    await container.createIfNotExists();
    const fileName = file instanceof File ? file.name : 'policy.pdf';
    const blobName = `${Date.now()}_${fileName}`;
    const blockBlob = container.getBlockBlobClient(blobName);
    const arrayBuffer = await file.arrayBuffer();
    await blockBlob.uploadData(arrayBuffer, { blobHTTPHeaders: { blobContentType: 'application/pdf' } });
    const fileUrl = blockBlob.url;

    // Insert metadata into SQL
    const pool = await getPool();
    const insertQuery = `
      INSERT INTO Policies (Title, Type, Description, Tags, Version, FileUrl, LastUpdated, UploadedBy, IsActive)
      VALUES (@title, @type, @description, @tags, @version, @fileUrl, GETDATE(), @uploadedBy, 1)
    `;
    await pool.request()
      .input('title', sql.NVarChar, title)
      .input('type', sql.NVarChar, type)
      .input('description', sql.NVarChar, description)
      .input('tags', sql.NVarChar, tags)
      .input('version', sql.NVarChar, version)
      .input('fileUrl', sql.NVarChar, fileUrl)
      .input('uploadedBy', sql.NVarChar, uploadedBy)
      .query(insertQuery);

    return { status: 201, jsonBody: { message: 'Policy uploaded successfully.', fileUrl } };
  } catch (err) {
    context.error('UploadPolicy error:', err);
    return { status: 500, jsonBody: { error: 'Failed to upload policy.' } };
  }
}

app.http('UploadPolicy', {
  methods: ['POST'],
  authLevel: 'function',
  handler: uploadPolicy
}); 