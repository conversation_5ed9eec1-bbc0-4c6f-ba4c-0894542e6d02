import { app, HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
import { getPool } from '../shared/db';
import * as sql from 'mssql';

export async function getPolicy(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  try {
    const id = req.params.id;
    if (!id) {
      return { status: 400, jsonBody: { error: 'Policy ID is required.' } };
    }
    const pool = await getPool();
    const query = 'SELECT * FROM Policies WHERE PolicyID = @id AND IsActive = 1';
    const result = await pool.request().input('id', sql.Int, id).query(query);
    if (!result.recordset || result.recordset.length === 0) {
      return { status: 404, jsonBody: { error: 'Policy not found.' } };
    }
    const policy = result.recordset[0];
    return {
      status: 200,
      jsonBody: policy
    };
  } catch (err) {
    context.error('GetPolicy error:', err);
    return { status: 500, jsonBody: { error: 'Failed to fetch policy.' } };
  }
}

app.http('GetPolicy', {
  methods: ['GET'],
  authLevel: 'function',
  handler: getPolicy
}); 