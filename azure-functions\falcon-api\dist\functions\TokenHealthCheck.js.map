{"version": 3, "file": "TokenHealthCheck.js", "sourceRoot": "", "sources": ["../../src/functions/TokenHealthCheck.ts"], "names": [], "mappings": ";;;AAAA,gDAAyF;AACzF,mDAAgD;AAChD,qCAAuC;AAuBhC,KAAK,UAAU,gBAAgB,CAAC,OAAoB,EAAE,OAA0B;IACnF,eAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;IAElE,IAAI;QACA,MAAM,IAAI,GAAG,MAAM,IAAA,YAAO,GAAE,CAAC;QAE7B,8BAA8B;QAC9B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE;aAC9B,KAAK,CAAC;;;;;;;;;;;;;aAaN,CAAC,CAAC;QAEP,MAAM,aAAa,GAAwB,EAAE,CAAC;QAC9C,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,IAAI,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACjD,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE;gBACnC,MAAM,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,IAAI,CAAC,CAAC;gBAC1D,IAAI,MAAqD,CAAC;gBAE1D,IAAI,kBAAkB,IAAI,CAAC,EAAE;oBACzB,MAAM,GAAG,SAAS,CAAC;oBACnB,YAAY,EAAE,CAAC;iBAClB;qBAAM,IAAI,kBAAkB,IAAI,EAAE,EAAE;oBACjC,MAAM,GAAG,SAAS,CAAC;oBACnB,YAAY,EAAE,CAAC;iBAClB;qBAAM;oBACH,MAAM,GAAG,SAAS,CAAC;oBACnB,YAAY,EAAE,CAAC;iBAClB;gBAED,aAAa,CAAC,IAAI,CAAC;oBACf,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,eAAe,EAAE,MAAM,CAAC,eAAe;oBACvC,WAAW,EAAE,MAAM,CAAC,WAAW;oBAC/B,SAAS,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;oBACrC,kBAAkB,EAAE,kBAAkB;oBACtC,MAAM,EAAE,MAAM;oBACd,eAAe,EAAE,CAAC,CAAC,MAAM,CAAC,YAAY;iBACzC,CAAC,CAAC;aACN;SACJ;QAED,mDAAmD;QACnD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE;aAClC,KAAK,CAAC;;;;;;;;;;;aAWN,CAAC,CAAC;QAEP,MAAM,YAAY,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAE5E,4BAA4B;QAC5B,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACzD,KAAK,MAAM,UAAU,IAAI,UAAU,CAAC,SAAS,EAAE;gBAC3C,aAAa,CAAC,IAAI,CAAC;oBACf,MAAM,EAAE,UAAU,CAAC,MAAM;oBACzB,eAAe,EAAE,MAAM;oBACvB,WAAW,EAAE,MAAM;oBACnB,SAAS,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC;oBACtB,kBAAkB,EAAE,CAAC,CAAC;oBACtB,MAAM,EAAE,SAAS;oBACjB,eAAe,EAAE,KAAK;iBACzB,CAAC,CAAC;aACN;SACJ;QAED,MAAM,OAAO,GAAuB;YAChC,WAAW,EAAE,aAAa,CAAC,MAAM;YACjC,aAAa,EAAE,YAAY;YAC3B,aAAa,EAAE,YAAY;YAC3B,aAAa,EAAE,YAAY;YAC3B,aAAa,EAAE,YAAY;YAC3B,WAAW,EAAE,IAAI,IAAI,EAAE;YACvB,OAAO,EAAE,aAAa;SACzB,CAAC;QAEF,kCAAkC;QAClC,MAAM,aAAa,GAAG,YAAY,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;YACpD,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;QAE9D,eAAM,CAAC,IAAI,CAAC,6CAA6C,OAAO,CAAC,WAAW,cAAc,YAAY,cAAc,YAAY,cAAc,YAAY,cAAc,YAAY,EAAE,CAAC,CAAC;QAExL,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,aAAa,EAAE,aAAa;gBAC5B,OAAO,EAAE,OAAO;gBAChB,eAAe,EAAE,uBAAuB,CAAC,OAAO,CAAC;aACpD;SACJ,CAAC;KAEL;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;QACpE,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,KAAK,EAAE,sCAAsC;gBAC7C,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAClE;SACJ,CAAC;KACL;AACL,CAAC;AA3HD,4CA2HC;AAED,SAAS,uBAAuB,CAAC,OAA2B;IACxD,MAAM,eAAe,GAAa,EAAE,CAAC;IAErC,IAAI,OAAO,CAAC,aAAa,GAAG,CAAC,EAAE;QAC3B,eAAe,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,aAAa,kDAAkD,CAAC,CAAC;KACpG;IAED,IAAI,OAAO,CAAC,aAAa,GAAG,CAAC,EAAE;QAC3B,eAAe,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,aAAa,oDAAoD,CAAC,CAAC;KACtG;IAED,IAAI,OAAO,CAAC,aAAa,GAAG,CAAC,EAAE;QAC3B,eAAe,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,aAAa,+CAA+C,CAAC,CAAC;KACjG;IAED,IAAI,OAAO,CAAC,aAAa,KAAK,OAAO,CAAC,WAAW,EAAE;QAC/C,eAAe,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;KAClD;IAED,gCAAgC;IAChC,eAAe,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;IACtE,eAAe,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;IACtE,eAAe,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;IAE7E,OAAO,eAAe,CAAC;AAC3B,CAAC;AAED,uCAAuC;AACvC,eAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE;IACzB,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,SAAS,EAAE,WAAW;IACtB,KAAK,EAAE,oBAAoB;IAC3B,OAAO,EAAE,gBAAgB;CAC5B,CAAC,CAAC"}