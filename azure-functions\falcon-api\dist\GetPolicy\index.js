"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPolicy = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const sql = __importStar(require("mssql"));
async function getPolicy(req, context) {
    try {
        const id = req.params.id;
        if (!id) {
            return { status: 400, jsonBody: { error: 'Policy ID is required.' } };
        }
        const pool = await (0, db_1.getPool)();
        const query = 'SELECT * FROM Policies WHERE PolicyID = @id AND IsActive = 1';
        const result = await pool.request().input('id', sql.Int, id).query(query);
        if (!result.recordset || result.recordset.length === 0) {
            return { status: 404, jsonBody: { error: 'Policy not found.' } };
        }
        const policy = result.recordset[0];
        return {
            status: 200,
            jsonBody: policy
        };
    }
    catch (err) {
        context.error('GetPolicy error:', err);
        return { status: 500, jsonBody: { error: 'Failed to fetch policy.' } };
    }
}
exports.getPolicy = getPolicy;
functions_1.app.http('GetPolicy', {
    methods: ['GET'],
    authLevel: 'function',
    handler: getPolicy
});
//# sourceMappingURL=index.js.map