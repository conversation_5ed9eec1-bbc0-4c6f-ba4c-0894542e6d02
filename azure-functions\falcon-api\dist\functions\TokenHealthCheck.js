"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.tokenHealthCheck = void 0;
const functions_1 = require("@azure/functions");
const logger_1 = require("../shared/utils/logger");
const db_1 = require("../shared/db");
async function tokenHealthCheck(request, context) {
    logger_1.logger.info('TokenHealthCheck: Starting token health assessment');
    try {
        const pool = await (0, db_1.getPool)();
        // Get all active OAuth tokens
        const result = await pool.request()
            .query(`
                SELECT 
                    UserId,
                    ServiceProvider,
                    ServiceType,
                    ExpiresAt,
                    RefreshToken,
                    DATEDIFF(MINUTE, GETUTCDATE(), ExpiresAt) as MinutesUntilExpiry
                FROM OAuthTokens 
                WHERE IsActive = 1 
                  AND ServiceProvider = 'zoho' 
                  AND ServiceType = 'desk'
                ORDER BY ExpiresAt ASC
            `);
        const tokenStatuses = [];
        let healthyCount = 0;
        let warningCount = 0;
        let expiredCount = 0;
        if (result.recordset && result.recordset.length > 0) {
            for (const record of result.recordset) {
                const minutesUntilExpiry = record.MinutesUntilExpiry || 0;
                let status;
                if (minutesUntilExpiry <= 0) {
                    status = 'expired';
                    expiredCount++;
                }
                else if (minutesUntilExpiry <= 10) {
                    status = 'warning';
                    warningCount++;
                }
                else {
                    status = 'healthy';
                    healthyCount++;
                }
                tokenStatuses.push({
                    userId: record.UserId,
                    serviceProvider: record.ServiceProvider,
                    serviceType: record.ServiceType,
                    expiresAt: new Date(record.ExpiresAt),
                    minutesUntilExpiry: minutesUntilExpiry,
                    status: status,
                    hasRefreshToken: !!record.RefreshToken
                });
            }
        }
        // Check for users who should have tokens but don't
        const userResult = await pool.request()
            .query(`
                SELECT DISTINCT u.EntraID as UserId
                FROM Users u
                WHERE u.IsActive = 1
                  AND NOT EXISTS (
                      SELECT 1 FROM OAuthTokens ot 
                      WHERE ot.UserId = u.EntraID 
                        AND ot.ServiceProvider = 'zoho' 
                        AND ot.ServiceType = 'desk' 
                        AND ot.IsActive = 1
                  )
            `);
        const missingCount = userResult.recordset ? userResult.recordset.length : 0;
        // Add missing token entries
        if (userResult.recordset && userResult.recordset.length > 0) {
            for (const userRecord of userResult.recordset) {
                tokenStatuses.push({
                    userId: userRecord.UserId,
                    serviceProvider: 'zoho',
                    serviceType: 'desk',
                    expiresAt: new Date(0),
                    minutesUntilExpiry: -1,
                    status: 'missing',
                    hasRefreshToken: false
                });
            }
        }
        const summary = {
            totalTokens: tokenStatuses.length,
            healthyTokens: healthyCount,
            warningTokens: warningCount,
            expiredTokens: expiredCount,
            missingTokens: missingCount,
            lastChecked: new Date(),
            details: tokenStatuses
        };
        // Determine overall health status
        const overallStatus = expiredCount > 0 || missingCount > 0 ? 'critical' :
            warningCount > 0 ? 'warning' : 'healthy';
        logger_1.logger.info(`TokenHealthCheck: Health summary - Total: ${summary.totalTokens}, Healthy: ${healthyCount}, Warning: ${warningCount}, Expired: ${expiredCount}, Missing: ${missingCount}`);
        return {
            status: 200,
            jsonBody: {
                overallStatus: overallStatus,
                summary: summary,
                recommendations: generateRecommendations(summary)
            }
        };
    }
    catch (error) {
        logger_1.logger.error('TokenHealthCheck: Error during health check:', error);
        return {
            status: 500,
            jsonBody: {
                error: 'Failed to perform token health check',
                details: error instanceof Error ? error.message : String(error)
            }
        };
    }
}
exports.tokenHealthCheck = tokenHealthCheck;
function generateRecommendations(summary) {
    const recommendations = [];
    if (summary.expiredTokens > 0) {
        recommendations.push(`${summary.expiredTokens} token(s) have expired and need re-authorization`);
    }
    if (summary.warningTokens > 0) {
        recommendations.push(`${summary.warningTokens} token(s) will expire soon and should be refreshed`);
    }
    if (summary.missingTokens > 0) {
        recommendations.push(`${summary.missingTokens} user(s) need to complete OAuth authorization`);
    }
    if (summary.healthyTokens === summary.totalTokens) {
        recommendations.push('All tokens are healthy');
    }
    // Add proactive recommendations
    recommendations.push('Consider implementing automated token refresh');
    recommendations.push('Set up monitoring alerts for token expiration');
    recommendations.push('Review token usage patterns in OAuthTokenUsage table');
    return recommendations;
}
// Register the HTTP-triggered function
functions_1.app.http('TokenHealthCheck', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'admin/token-health',
    handler: tokenHealthCheck
});
//# sourceMappingURL=TokenHealthCheck.js.map