{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/GetPolicy/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAyF;AACzF,qCAAuC;AACvC,2CAA6B;AAEtB,KAAK,UAAU,SAAS,CAAC,GAAgB,EAAE,OAA0B;IAC1E,IAAI;QACF,MAAM,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;QACzB,IAAI,CAAC,EAAE,EAAE;YACP,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,wBAAwB,EAAE,EAAE,CAAC;SACvE;QACD,MAAM,IAAI,GAAG,MAAM,IAAA,YAAO,GAAE,CAAC;QAC7B,MAAM,KAAK,GAAG,8DAA8D,CAAC;QAC7E,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC1E,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACtD,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,CAAC;SAClE;QACD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACnC,OAAO;YACL,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE,MAAM;SACjB,CAAC;KACH;IAAC,OAAO,GAAG,EAAE;QACZ,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC;QACvC,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,CAAC;KACxE;AACH,CAAC;AArBD,8BAqBC;AAED,eAAG,CAAC,IAAI,CAAC,WAAW,EAAE;IACpB,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,SAAS,EAAE,UAAU;IACrB,OAAO,EAAE,SAAS;CACnB,CAAC,CAAC"}