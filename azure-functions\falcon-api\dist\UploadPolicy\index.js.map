{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/UploadPolicy/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAyF;AACzF,qCAAuC;AACvC,2CAA6B;AAC7B,sDAAwD;AAExD,SAAS,iBAAiB,CAAC,QAAkB,EAAE;IAC7C,OAAO,KAAK,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;AACvE,CAAC;AAEM,KAAK,UAAU,YAAY,CAAC,GAAgB,EAAE,OAA0B;IAC7E,IAAI;QACF,MAAM,SAAS,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QACnF,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE;YACjC,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,sCAAsC,EAAE,EAAE,CAAC;SACrF;QAED,wDAAwD;QACxD,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,QAAQ,EAAE,CAAC;QACtC,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAClC,IAAI,CAAC,CAAC,IAAI,YAAY,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,iBAAiB,EAAE;YAC9D,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,6BAA6B,EAAE,EAAE,CAAC;SAC5E;QACD,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAW,CAAC;QAC9C,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAW,CAAC;QAC5C,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAW,CAAC;QAC1D,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAW,CAAC;QAC5C,MAAM,OAAO,GAAI,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAY,IAAI,MAAM,CAAC;QAC9D,MAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAW,IAAI,SAAS,CAAC;QAErE,mCAAmC;QACnC,MAAM,WAAW,GAAG,gCAAiB,CAAC,oBAAoB,CAAC,OAAO,CAAC,GAAG,CAAC,+BAAgC,CAAC,CAAC;QACzG,MAAM,SAAS,GAAG,WAAW,CAAC,kBAAkB,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,UAAU,CAAC,CAAC;QACpG,MAAM,SAAS,CAAC,iBAAiB,EAAE,CAAC;QACpC,MAAM,QAAQ,GAAG,IAAI,YAAY,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC;QACjE,MAAM,QAAQ,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,QAAQ,EAAE,CAAC;QAC7C,MAAM,SAAS,GAAG,SAAS,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QACzD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QAC7C,MAAM,SAAS,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE,eAAe,EAAE,EAAE,eAAe,EAAE,iBAAiB,EAAE,EAAE,CAAC,CAAC;QACrG,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC;QAE9B,2BAA2B;QAC3B,MAAM,IAAI,GAAG,MAAM,IAAA,YAAO,GAAE,CAAC;QAC7B,MAAM,WAAW,GAAG;;;KAGnB,CAAC;QACF,MAAM,IAAI,CAAC,OAAO,EAAE;aACjB,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;aACnC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC;aACjC,KAAK,CAAC,aAAa,EAAE,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC;aAC/C,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC;aACjC,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC;aACvC,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC;aACvC,KAAK,CAAC,YAAY,EAAE,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC;aAC7C,KAAK,CAAC,WAAW,CAAC,CAAC;QAEtB,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE,OAAO,EAAE,EAAE,CAAC;KACzF;IAAC,OAAO,GAAG,EAAE;QACZ,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,GAAG,CAAC,CAAC;QAC1C,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,0BAA0B,EAAE,EAAE,CAAC;KACzE;AACH,CAAC;AApDD,oCAoDC;AAED,eAAG,CAAC,IAAI,CAAC,cAAc,EAAE;IACvB,OAAO,EAAE,CAAC,MAAM,CAAC;IACjB,SAAS,EAAE,UAAU;IACrB,OAAO,EAAE,YAAY;CACtB,CAAC,CAAC"}