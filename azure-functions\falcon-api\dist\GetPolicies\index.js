"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPolicies = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const sql = __importStar(require("mssql"));
async function getPolicies(req, context) {
    try {
        const page = parseInt(req.query.get('page') || '1');
        const pageSize = parseInt(req.query.get('pageSize') || '10');
        const search = req.query.get('search') || '';
        const type = req.query.get('type') || '';
        const pool = await (0, db_1.getPool)();
        let where = 'WHERE IsActive = 1';
        const params = [];
        if (search) {
            where += ' AND (Title LIKE @search OR Description LIKE @search OR Tags LIKE @search)';
            params.push({ name: 'search', type: sql.NVarChar, value: `%${search}%` });
        }
        if (type) {
            where += ' AND Type = @type';
            params.push({ name: 'type', type: sql.NVarChar, value: type });
        }
        const offset = (page - 1) * pageSize;
        const query = `
      SELECT * FROM Policies
      ${where}
      ORDER BY LastUpdated DESC
      OFFSET @offset ROWS FETCH NEXT @pageSize ROWS ONLY;
      SELECT COUNT(*) as total FROM Policies ${where};
    `;
        const request = pool.request();
        params.forEach(p => request.input(p.name, p.type, p.value));
        request.input('offset', sql.Int, offset);
        request.input('pageSize', sql.Int, pageSize);
        const result = await request.query(query);
        const policies = (result.recordsets && Array.isArray(result.recordsets) && result.recordsets[0]) ? result.recordsets[0] : [];
        const total = (result.recordsets && Array.isArray(result.recordsets) && result.recordsets[1] && result.recordsets[1][0]) ? result.recordsets[1][0].total : 0;
        return {
            status: 200,
            jsonBody: {
                policies,
                total,
                page,
                pageSize
            }
        };
    }
    catch (err) {
        context.error('GetPolicies error:', err);
        return {
            status: 500,
            jsonBody: { error: 'Failed to fetch policies.' }
        };
    }
}
exports.getPolicies = getPolicies;
functions_1.app.http('GetPolicies', {
    methods: ['GET'],
    authLevel: 'function',
    handler: getPolicies
});
//# sourceMappingURL=index.js.map