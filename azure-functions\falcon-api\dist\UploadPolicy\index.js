"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.uploadPolicy = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const sql = __importStar(require("mssql"));
const storage_blob_1 = require("@azure/storage-blob");
function hasElevatedAccess(roles = []) {
    return roles.includes('Administrator') || roles.includes('IT Admin');
}
async function uploadPolicy(req, context) {
    try {
        const userRoles = (req.headers.get('x-roles') || '').split(',').map(r => r.trim());
        if (!hasElevatedAccess(userRoles)) {
            return { status: 403, jsonBody: { error: 'Forbidden: Insufficient permissions.' } };
        }
        // Parse form-data (assume PDF file and metadata fields)
        const formData = await req.formData();
        const file = formData.get('file');
        if (!(file instanceof Blob) || file.type !== 'application/pdf') {
            return { status: 400, jsonBody: { error: 'Only PDF files are allowed.' } };
        }
        const title = formData.get('title');
        const type = formData.get('type');
        const description = formData.get('description');
        const tags = formData.get('tags');
        const version = formData.get('version') || 'v1.0';
        const uploadedBy = formData.get('uploadedBy') || 'Unknown';
        // Upload PDF to Azure Blob Storage
        const blobService = storage_blob_1.BlobServiceClient.fromConnectionString(process.env.AZURE_STORAGE_CONNECTION_STRING);
        const container = blobService.getContainerClient(process.env.POLICIES_BLOB_CONTAINER || 'policies');
        await container.createIfNotExists();
        const fileName = file instanceof File ? file.name : 'policy.pdf';
        const blobName = `${Date.now()}_${fileName}`;
        const blockBlob = container.getBlockBlobClient(blobName);
        const arrayBuffer = await file.arrayBuffer();
        await blockBlob.uploadData(arrayBuffer, { blobHTTPHeaders: { blobContentType: 'application/pdf' } });
        const fileUrl = blockBlob.url;
        // Insert metadata into SQL
        const pool = await (0, db_1.getPool)();
        const insertQuery = `
      INSERT INTO Policies (Title, Type, Description, Tags, Version, FileUrl, LastUpdated, UploadedBy, IsActive)
      VALUES (@title, @type, @description, @tags, @version, @fileUrl, GETDATE(), @uploadedBy, 1)
    `;
        await pool.request()
            .input('title', sql.NVarChar, title)
            .input('type', sql.NVarChar, type)
            .input('description', sql.NVarChar, description)
            .input('tags', sql.NVarChar, tags)
            .input('version', sql.NVarChar, version)
            .input('fileUrl', sql.NVarChar, fileUrl)
            .input('uploadedBy', sql.NVarChar, uploadedBy)
            .query(insertQuery);
        return { status: 201, jsonBody: { message: 'Policy uploaded successfully.', fileUrl } };
    }
    catch (err) {
        context.error('UploadPolicy error:', err);
        return { status: 500, jsonBody: { error: 'Failed to upload policy.' } };
    }
}
exports.uploadPolicy = uploadPolicy;
functions_1.app.http('UploadPolicy', {
    methods: ['POST'],
    authLevel: 'function',
    handler: uploadPolicy
});
//# sourceMappingURL=index.js.map