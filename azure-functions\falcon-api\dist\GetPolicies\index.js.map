{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/GetPolicies/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAyF;AACzF,qCAAuC;AACvC,2CAA6B;AAEtB,KAAK,UAAU,WAAW,CAAC,GAAgB,EAAE,OAA0B;IAC5E,IAAI;QACF,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;QACpD,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,CAAC;QAC7D,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC7C,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QAEzC,MAAM,IAAI,GAAG,MAAM,IAAA,YAAO,GAAE,CAAC;QAC7B,IAAI,KAAK,GAAG,oBAAoB,CAAC;QACjC,MAAM,MAAM,GAA8C,EAAE,CAAC;QAE7D,IAAI,MAAM,EAAE;YACV,KAAK,IAAI,4EAA4E,CAAC;YACtF,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC,CAAC;SAC3E;QACD,IAAI,IAAI,EAAE;YACR,KAAK,IAAI,mBAAmB,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;SAChE;QAED,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC;QACrC,MAAM,KAAK,GAAG;;QAEV,KAAK;;;+CAGkC,KAAK;KAC/C,CAAC;QAEF,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC/B,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QAC5D,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QACzC,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QAE7C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC1C,MAAM,QAAQ,GAAG,CAAC,MAAM,CAAC,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7H,MAAM,KAAK,GAAG,CAAC,MAAM,CAAC,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAE7J,OAAO;YACL,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACR,QAAQ;gBACR,KAAK;gBACL,IAAI;gBACJ,QAAQ;aACT;SACF,CAAC;KACH;IAAC,OAAO,GAAG,EAAE;QACZ,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;QACzC,OAAO;YACL,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE,EAAE,KAAK,EAAE,2BAA2B,EAAE;SACjD,CAAC;KACH;AACH,CAAC;AAtDD,kCAsDC;AAED,eAAG,CAAC,IAAI,CAAC,aAAa,EAAE;IACtB,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,SAAS,EAAE,UAAU;IACrB,OAAO,EAAE,WAAW;CACrB,CAAC,CAAC"}